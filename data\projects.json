{"default": "todeskyundiannao", "projects": {"todeskyundiannao": {"id": "todeskyundiannao", "name": "todesk云电脑", "description": "", "createdAt": "2025-06-17T08:39:19.217Z", "yeziProjectId": "15487", "haozhuProjectId": "88261", "yeziExcludePrefix": "", "haozhuExcludePrefix": "192", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "idCardConfig": {"enabled": true}, "guideConfig": {"enabled": true, "content": ["获取号码后需在**1分钟内**于客户端发送验证码", "每个号码仅能获取**1次**验证码，请勿重复获取", "未收到验证码时，系统会自动尝试**60次**，失败后可更换号码", "验证码有效期通常为5分钟，请及时使用", "身份证号码可更换**5次**，请谨慎使用"]}, "popupConfig": {"enabled": false, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}, "youquConfig": {"enabled": false, "claimDelay": 3, "deviceModel": "Xiaomi+24031PN0DC", "appVersion": "4.6.0", "promoId": "188509664637552640", "apiEndpoints": {"sms": "https://fastapi.ukids.cn/ucapp/sms", "login": "https://fastapi.ukids.cn/ucapp/mobileLogin", "weekCard": "https://fastapi.ukids.cn/saleapp/promo/gift/receive"}}}, "todeskyuankong": {"id": "todeskyuankong", "name": "todesk远控", "description": "", "createdAt": "2025-06-17T08:39:34.057Z", "yeziProjectId": "", "haozhuProjectId": "54283", "yeziExcludePrefix": "", "haozhuExcludePrefix": "", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "idCardConfig": {"enabled": false}, "guideConfig": {"enabled": true, "content": ["获取号码后需在**1分钟内**于客户端发送验证码", "每个号码仅能获取**1次**验证码，请勿重复获取", "未收到验证码时，系统会自动尝试**60次**，失败后可更换号码", "验证码有效期通常为5分钟，请及时使用", "身份证号码可更换**5次**，请谨慎使用"]}, "popupConfig": {"enabled": false, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}}, "yuantiaoshi": {"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "源调试", "description": "", "createdAt": "2025-06-17T08:39:56.008Z", "yeziProjectId": "", "haozhuProjectId": "97033", "yeziExcludePrefix": "", "haozhuExcludePrefix": "", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "idCardConfig": {"enabled": false}, "guideConfig": {"enabled": true, "content": ["获取号码后需在**1分钟内**于客户端发送验证码", "每个号码仅能获取**1次**验证码，请勿重复获取", "未收到验证码时，系统会自动尝试**60次**，失败后可更换号码", "验证码有效期通常为5分钟，请及时使用", "身份证号码可更换**5次**，请谨慎使用"]}, "popupConfig": {"enabled": false, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}}, "xunyoujiasuqi": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "迅游加速器", "description": "", "createdAt": "2025-06-17T08:45:00.631Z", "yeziProjectId": "", "haozhuProjectId": "24085", "yeziExcludePrefix": "", "haozhuExcludePrefix": "", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "idCardConfig": {"enabled": true}, "guideConfig": {"enabled": true, "content": ["获取号码后需在**1分钟内**于客户端发送验证码", "每个号码仅能获取**1次**验证码，请勿重复获取", "未收到验证码时，系统会自动尝试**60次**，失败后可更换号码", "验证码有效期通常为5分钟，请及时使用", "身份证号码可更换**5次**，请谨慎使用"]}, "popupConfig": {"enabled": false, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}, "youquConfig": {"enabled": false, "claimDelay": 3, "deviceModel": "Xiaomi+24031PN0DC", "appVersion": "4.6.0", "promoId": "188509664637552640", "apiEndpoints": {"sms": "https://fastapi.ukids.cn/ucapp/sms", "login": "https://fastapi.ukids.cn/ucapp/mobileLogin", "weekCard": "https://fastapi.ukids.cn/saleapp/promo/gift/receive"}}}, "xiangsudangao": {"id": "<PERSON>ian<PERSON>udangao", "name": "像素蛋糕", "description": "像素蛋糕", "createdAt": "2025-06-25T05:34:10.615Z", "yeziProjectId": "", "haozhuProjectId": "54188", "yeziExcludePrefix": "", "haozhuExcludePrefix": "", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "idCardConfig": {"enabled": false}, "guideConfig": {"enabled": true, "content": ["获取号码后需在**1分钟内**于客户端发送验证码", "每个号码仅能获取**1次**验证码，请勿重复获取", "未收到验证码时，系统会自动尝试**60次**，失败后可更换号码", "验证码有效期通常为5分钟，请及时使用", "身份证号码可更换**5次**，请谨慎使用"]}, "popupConfig": {"enabled": false, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}}, "shunwangyundiannao": {"id": "<PERSON><PERSON>wang<PERSON>dianna<PERSON>", "name": "顺网云电脑", "description": "顺网云电脑", "createdAt": "2025-06-28T08:13:48.737Z", "yeziProjectId": "", "haozhuProjectId": "26343", "yeziExcludePrefix": "", "haozhuExcludePrefix": "", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "idCardConfig": {"enabled": false}, "guideConfig": {"enabled": true, "content": ["获取号码后需在**1分钟内**于客户端发送验证码", "每个号码仅能获取**1次**验证码，请勿重复获取", "未收到验证码时，系统会自动尝试**60次**，失败后可更换号码", "验证码有效期通常为5分钟，请及时使用", "身份证号码可更换**5次**，请谨慎使用"]}, "popupConfig": {"enabled": false, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}}, "leishenjiasuqi": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "雷神加速器", "description": "雷神加速器", "createdAt": "2025-06-28T09:10:08.742Z", "yeziProjectId": "802685----L9I17Z", "haozhuProjectId": "82898", "yeziExcludePrefix": "", "haozhuExcludePrefix": "", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "idCardConfig": {"enabled": false}, "guideConfig": {"enabled": true, "content": ["获取号码后需在**1分钟内**于客户端发送验证码", "每个号码仅能获取**1次**验证码，请勿重复获取", "未收到验证码时，系统会自动尝试**60次**，失败后可更换号码", "验证码有效期通常为5分钟，请及时使用", "身份证号码可更换**5次**，请谨慎使用"]}, "popupConfig": {"enabled": false, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}}, "xiaoxiaoyouqu": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "小小优趣", "description": "小小优趣", "createdAt": "2025-07-02T00:00:00.000Z", "yeziProjectId": "15487", "haozhuProjectId": "56208", "yeziExcludePrefix": "192,135", "haozhuExcludePrefix": "192", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "youquConfig": {"enabled": true, "claimDelay": 1, "deviceModel": "Xiaomi+24031PN0DC", "appVersion": "4.6.0", "promoId": "188509664637552640", "apiEndpoints": {"sms": "https://fastapi.ukids.cn/ucapp/sms", "login": "https://fastapi.ukids.cn/ucapp/mobileLogin", "weekCard": "https://fastapi.ukids.cn/saleapp/promo/gift/receive"}}, "idCardConfig": {"enabled": false}, "guideConfig": {"enabled": true, "content": ["获取号码后尽快拿去小小优趣软件发送验证码", "发送验证码以后回到当前界面等待出现验证码", "获得验证码后输入到小小优趣软件进行登录", "登录成功后周卡VIP会在一分钟后到账", "出现验证码后请在半分钟内拿去登录", "不然出现的验证码会失效！！且后台会进行领取周卡", "这种情况不售后！不退款！"]}, "popupConfig": {"enabled": true, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}, "yeziOperator": "4", "haozhuAscription": "2"}, "haimayundiannao": {"id": "haimayundiannao", "name": "海马云电脑", "description": "", "createdAt": "2025-07-25T10:34:45.205Z", "yeziProjectId": "", "haozhuProjectId": "76980", "yeziExcludePrefix": "", "haozhuExcludePrefix": "192", "haozhuAscription": "2", "preferred_platform": "<PERSON><PERSON>hu", "active": true, "youquConfig": {"enabled": false, "claimDelay": 3, "deviceModel": "Xiaomi+24031PN0DC", "appVersion": "4.6.0", "promoId": "188509664637552640", "apiEndpoints": {"sms": "https://fastapi.ukids.cn/ucapp/sms", "login": "https://fastapi.ukids.cn/ucapp/mobileLogin", "weekCard": "https://fastapi.ukids.cn/saleapp/promo/gift/receive"}}, "idCardConfig": {"enabled": false}, "guideConfig": {"enabled": true, "content": ["获取号码后需在1分钟内于客户端发送验证码", "每个号码仅能获取1次验证码，请勿重复获取", "未收到验证码时，系统会自动尝试60次，失败后可更换号码", "验证码有效期通常为5分钟，请及时使用", "身份证号码可更换5次，请谨慎使用"]}, "popupConfig": {"enabled": false, "title": "温馨提示", "content": "请仔细阅读操作指南，确保正确使用验证码服务。", "showOnLoad": true, "showOnGetPhone": false, "buttonText": "我知道了"}}}}