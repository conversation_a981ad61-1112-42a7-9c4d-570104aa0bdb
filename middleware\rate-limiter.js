/**
 * 频率限制中间件
 * 基于内存的简单频率限制实现
 */

// 存储IP访问记录
const ipAccessMap = new Map();

// 配置
const RATE_LIMIT_CONFIG = {
  windowMs: 60 * 1000, // 1分钟窗口
  maxRequests: 100,    // 每分钟最多100次请求
  message: '请求过于频繁，请稍后再试'
};

/**
 * 清理过期记录
 */
function cleanupExpiredRecords() {
  const now = Date.now();
  for (const [ip, record] of ipAccessMap.entries()) {
    if (now - record.windowStart > RATE_LIMIT_CONFIG.windowMs) {
      ipAccessMap.delete(ip);
    }
  }
}

/**
 * 频率限制中间件
 * @param {object} options - 配置选项
 * @returns {function} Express中间件函数
 */
function createRateLimiter(options = {}) {
  const config = {
    ...RATE_LIMIT_CONFIG,
    ...options
  };

  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();

    // 定期清理过期记录
    if (Math.random() < 0.1) { // 10%的概率执行清理
      cleanupExpiredRecords();
    }

    // 获取或创建IP记录
    let record = ipAccessMap.get(clientIP);
    
    if (!record || (now - record.windowStart) > config.windowMs) {
      // 新窗口或新IP
      record = {
        windowStart: now,
        requestCount: 1
      };
      ipAccessMap.set(clientIP, record);
      return next();
    }

    // 在当前窗口内
    record.requestCount++;

    if (record.requestCount > config.maxRequests) {
      // 超过限制
      console.log(`🚫 IP ${clientIP} 触发频率限制: ${record.requestCount}/${config.maxRequests} 请求`);
      
      return res.status(429).json({
        success: false,
        error: config.message,
        details: {
          limit: config.maxRequests,
          windowMs: config.windowMs,
          retryAfter: Math.ceil((record.windowStart + config.windowMs - now) / 1000)
        }
      });
    }

    // 添加响应头
    res.set({
      'X-RateLimit-Limit': config.maxRequests,
      'X-RateLimit-Remaining': Math.max(0, config.maxRequests - record.requestCount),
      'X-RateLimit-Reset': new Date(record.windowStart + config.windowMs).toISOString()
    });

    next();
  };
}

/**
 * 获取频率限制统计
 * @returns {object} 统计信息
 */
function getRateLimitStats() {
  const now = Date.now();
  const activeIPs = [];
  
  for (const [ip, record] of ipAccessMap.entries()) {
    if (now - record.windowStart <= RATE_LIMIT_CONFIG.windowMs) {
      activeIPs.push({
        ip,
        requestCount: record.requestCount,
        windowStart: new Date(record.windowStart).toISOString(),
        remaining: Math.max(0, RATE_LIMIT_CONFIG.maxRequests - record.requestCount)
      });
    }
  }

  return {
    totalActiveIPs: activeIPs.length,
    activeIPs: activeIPs.sort((a, b) => b.requestCount - a.requestCount),
    config: RATE_LIMIT_CONFIG,
    generatedAt: new Date().toISOString()
  };
}

/**
 * 重置特定IP的限制
 * @param {string} ip - IP地址
 * @returns {boolean} 是否成功重置
 */
function resetIPLimit(ip) {
  if (ipAccessMap.has(ip)) {
    ipAccessMap.delete(ip);
    console.log(`🔄 重置IP ${ip} 的频率限制`);
    return true;
  }
  return false;
}

/**
 * 清除所有频率限制记录
 */
function clearAllLimits() {
  const count = ipAccessMap.size;
  ipAccessMap.clear();
  console.log(`🧹 清除了 ${count} 个IP的频率限制记录`);
  return count;
}

module.exports = {
  createRateLimiter,
  getRateLimitStats,
  resetIPLimit,
  clearAllLimits,
  RATE_LIMIT_CONFIG
};
