// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Mask > Affect alpha image with a bigger gray mask 1`] = `
Visualization:

▴812
66▾▿
66▾▿

Data:

FF-00-00⁰⁰ FF-00-00²¹ 00-00-FF⁸⁸ 00-00-FF⁴³
FF-00-00⁸⁸ FF-00-00⁶⁵ 00-00-FFᶠᶠ 00-00-FF⁷ᶠ
FF-00-00⁸⁸ FF-00-00⁶⁵ 00-00-FFᶠᶠ 00-00-FF⁷ᶠ
`;

exports[`Mask > Affect alpha image with a bigger gray mask, blited 1`] = `
Visualization:

4▵▾1
4▵▾1
8713

Data:

FF-00-00ᶜᶜ FF-00-00⁷ᶠ 00-00-FFᶠᶠ 00-00-FF⁶⁵
FF-00-00ᶜᶜ FF-00-00⁷ᶠ 00-00-FFᶠᶠ 00-00-FF⁶⁵
FF-00-00⁴⁴ FF-00-00⁴³ 00-00-FF⁸⁸ 00-00-FF²¹
`;

exports[`Mask > Affect opaque image with a colored mask 1`] = `
Visualization:

▴□▾□■□
■!▿▰▿□
■C5C0□
■□■▴■▾

Data:

FF-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-00-00⁵⁵ 00-00-00⁵⁵ 00-00-FF⁵⁵ 00-00-00⁵⁵ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FFᵃᵃ FF-00-00ᵃᵃ FF-FF-FFᵃᵃ 00-00-FFᵃᵃ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ FF-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-FFᶠᶠ
`;

exports[`Mask > Affect opaque image with a gray mask with the same size 1`] = `
Visualization:

▴F1DF0
D4■▾▥D
D▥▴□□D
 FD6F5

Data:

FF-00-00⁰⁰ FF-FF-FF⁴⁴ 00-00-FF⁸⁸ FF-FF-FF⁸⁸ 00-00-00⁴⁴ FF-FF-FF⁰⁰
00-00-00⁸⁸ FF-00-00ᶜᶜ 00-00-00ᶠᶠ 00-00-FFᶠᶠ 00-00-00ᶜᶜ FF-FF-FF⁸⁸
00-00-00⁸⁸ FF-FF-FFᶜᶜ FF-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-FFᶜᶜ FF-FF-FF⁸⁸
00-00-00⁰⁰ FF-FF-FF⁴⁴ 00-00-00⁸⁸ FF-00-00⁸⁸ 00-00-00⁴⁴ 00-00-FF⁰⁰
`;

exports[`Mask > Affect opaque image with a gray mask with the same size, blited 1`] = `
Visualization:

▴□▾□■□
■▴F1DF
■D4□▾▥
■D▥▴■□

Data:

FF-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-00-00⁰⁰ 00-00-00⁴⁴ 00-00-FF⁸⁸ 00-00-00⁸⁸ FF-FF-FF⁴⁴
00-00-00ᶠᶠ FF-FF-FF⁸⁸ FF-00-00ᶜᶜ FF-FF-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶜᶜ
00-00-00ᶠᶠ FF-FF-FF⁸⁸ 00-00-00ᶜᶜ FF-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-FFᶜᶜ
`;

exports[`Mask > Affect opaque image with a gray mask with the same size, blited negative 1`] = `
Visualization:

4□▾▥D□
▥▴■□D□
FD6F5□
■□■▴■▾

Data:

FF-00-00ᶜᶜ FF-FF-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶜᶜ 00-00-00⁸⁸ FF-FF-FFᶠᶠ
00-00-00ᶜᶜ FF-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-FFᶜᶜ 00-00-00⁸⁸ FF-FF-FFᶠᶠ
00-00-00⁴⁴ FF-FF-FF⁸⁸ FF-00-00⁸⁸ FF-FF-FF⁴⁴ 00-00-FF⁰⁰ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ FF-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-FFᶠᶠ
`;

exports[`Mask > Affect opaque image with a smaller gray mask 1`] = `
Visualization:

▴◆2▹■□
◆7▹□■□
E▹4□▾□
■□■▴■▾

Data:

FF-00-00⁰⁰ FF-FF-FF³³ 00-00-FF⁶⁶ FF-FF-FF⁹⁹ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00³³ FF-00-00⁶⁶ 00-00-00⁹⁹ 00-00-FFᶜᶜ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00⁶⁶ FF-FF-FF⁹⁹ FF-00-00ᶜᶜ FF-FF-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ FF-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-FFᶠᶠ
`;

exports[`Mask > Affect opaque image with a smaller gray mask, blited 1`] = `
Visualization:

▴□▾□■□
■▴◆2▹□
■◆7▹□□
■E▹4■▾

Data:

FF-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-00-00⁰⁰ 00-00-00³³ 00-00-FF⁶⁶ 00-00-00⁹⁹ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FF³³ FF-00-00⁶⁶ FF-FF-FF⁹⁹ 00-00-FFᶜᶜ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FF⁶⁶ 00-00-00⁹⁹ FF-00-00ᶜᶜ 00-00-00ᶠᶠ 00-00-FFᶠᶠ
`;
