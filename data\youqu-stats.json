{"daily": {"2025-07-02": {"date": "2025-07-02", "projects": {"xiaoxiaoyouqu": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣测试", "totalProcessed": 6, "phoneActivated": 2, "smsRequested": 2, "codeReceived": 2, "loginSuccess": 2, "weekCardClaimed": 2, "failed": 4, "successRate": 33.33333333333333, "avgPollingAttempts": 1.7083333333333333, "errors": {"获取指定手机号失败: 指定专属码手机号不在线": 2, "发送验证码失败: Request failed with status code 400": 1, "newCode is not defined": 1}}, "xiaoxiaoyouquceshi": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣测试", "totalProcessed": 4, "phoneActivated": 0, "smsRequested": 0, "codeReceived": 0, "loginSuccess": 0, "weekCardClaimed": 0, "failed": 4, "successRate": 0, "avgPollingAttempts": 0, "errors": {"获取指定手机号失败: Cannot read properties of null (reading 'getPlatformConfig')": 1, "获取指定手机号失败: 豪猪平台未登录，请先登录": 1, "获取指定手机号失败: 指定专属码手机号不在线": 1, "发送验证码失败: Request failed with status code 400": 1}}}, "summary": {"totalProcessed": 10, "totalSuccess": 2, "totalFailed": 8, "successRate": 20, "avgProcessingTime": 0}}, "2025-07-09": {"date": "2025-07-09", "projects": {"xiaoxiaoyouqu": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣", "totalProcessed": 1, "phoneActivated": 0, "smsRequested": 0, "codeReceived": 0, "loginSuccess": 0, "weekCardClaimed": 0, "failed": 1, "successRate": 0, "avgPollingAttempts": 0, "errors": {"获取指定手机号失败: 指定号码失败，您不能指定他人使用过的号码": 1}}}, "summary": {"totalProcessed": 1, "totalSuccess": 0, "totalFailed": 1, "successRate": 0, "avgProcessingTime": 0}}, "2025-07-16": {"date": "2025-07-16", "projects": {"xiaoxiaoyouqu": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣", "totalProcessed": 2, "phoneActivated": 2, "smsRequested": 2, "codeReceived": 2, "loginSuccess": 2, "weekCardClaimed": 2, "failed": 0, "successRate": 100, "avgPollingAttempts": 13, "errors": {}}}, "summary": {"totalProcessed": 2, "totalSuccess": 2, "totalFailed": 0, "successRate": 100, "avgProcessingTime": 0}}, "2025-07-18": {"date": "2025-07-18", "projects": {"xiaoxiaoyouqu": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣", "totalProcessed": 3, "phoneActivated": 2, "smsRequested": 2, "codeReceived": 2, "loginSuccess": 2, "weekCardClaimed": 2, "failed": 1, "successRate": 66.66666666666666, "avgPollingAttempts": 4.666666666666667, "errors": {"卡密不存在": 1}}}, "summary": {"totalProcessed": 3, "totalSuccess": 2, "totalFailed": 1, "successRate": 66.66666666666666, "avgProcessingTime": 0}}, "2025-07-21": {"date": "2025-07-21", "projects": {"xiaoxiaoyouqu": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣", "totalProcessed": 1, "phoneActivated": 0, "smsRequested": 0, "codeReceived": 0, "loginSuccess": 0, "weekCardClaimed": 0, "failed": 1, "successRate": 0, "avgPollingAttempts": 0, "errors": {"登录失败: 登录失败：未获取到token": 1}}}, "summary": {"totalProcessed": 1, "totalSuccess": 0, "totalFailed": 1, "successRate": 0, "avgProcessingTime": 0}}, "2025-07-23": {"date": "2025-07-23", "projects": {"xiaoxiaoyouqu": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣", "totalProcessed": 2, "phoneActivated": 2, "smsRequested": 2, "codeReceived": 2, "loginSuccess": 2, "weekCardClaimed": 2, "failed": 0, "successRate": 100, "avgPollingAttempts": 8, "errors": {}}}, "summary": {"totalProcessed": 2, "totalSuccess": 2, "totalFailed": 0, "successRate": 100, "avgProcessingTime": 0}}, "2025-07-24": {"date": "2025-07-24", "projects": {"xiaoxiaoyouqu": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣", "totalProcessed": 6, "phoneActivated": 5, "smsRequested": 5, "codeReceived": 5, "loginSuccess": 5, "weekCardClaimed": 5, "failed": 1, "successRate": 83.33333333333334, "avgPollingAttempts": 7.166666666666667, "errors": {"卡密未使用，请先获取验证码": 1}}}, "summary": {"totalProcessed": 6, "totalSuccess": 5, "totalFailed": 1, "successRate": 83.33333333333334, "avgProcessingTime": 0}}}, "monthly": {"2025-07": {"month": "2025-07", "totalProcessed": 25, "totalSuccess": 13, "totalFailed": 12, "successRate": 52, "projects": {"xiaoxiaoyouquceshi": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣测试", "totalProcessed": 4, "totalSuccess": 0, "successRate": 0}, "xiaoxiaoyouqu": {"projectId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "小小优趣", "totalProcessed": 21, "totalSuccess": 13, "successRate": 61.904761904761905}}}}, "overall": {"totalProcessed": 25, "totalSuccess": 13, "totalFailed": 12, "successRate": 52, "firstProcessedAt": "2025-07-02T11:32:27.249Z", "lastProcessedAt": "2025-07-24T06:58:05.288Z", "topProjects": [], "commonErrors": {"获取指定手机号失败: Cannot read properties of null (reading 'getPlatformConfig')": 1, "获取指定手机号失败: 豪猪平台未登录，请先登录": 1, "获取指定手机号失败: 指定专属码手机号不在线": 3, "发送验证码失败: Request failed with status code 400": 2, "newCode is not defined": 1, "获取指定手机号失败: 指定号码失败，您不能指定他人使用过的号码": 1, "卡密不存在": 1, "登录失败: 登录失败：未获取到token": 1, "卡密未使用，请先获取验证码": 1}}}