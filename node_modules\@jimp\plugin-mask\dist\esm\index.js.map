{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,eAAe,EAAE,MAAM,aAAa,CAAC;AACzD,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAExB,MAAM,uBAAuB,GAAG,CAAC,CAAC,MAAM,CAAC;IACvC,GAAG,EAAE,eAAe;IACpB,uCAAuC;IACvC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACxB,uCAAuC;IACvC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACzB,CAAC,CAAC;AAEH,MAAM,iBAAiB,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,uBAAuB,CAAC,CAAC,CAAC;AAI9E,MAAM,CAAC,MAAM,OAAO,GAAG;IACrB;;;;;;;;;;;;;;OAcG;IACH,IAAI,CAAsB,KAAQ,EAAE,OAAoB;QACtD,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEjC,IAAI,GAAc,CAAC;QACnB,IAAI,CAAS,CAAC;QACd,IAAI,CAAS,CAAC;QAEd,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;YACxB,GAAG,GAAG,OAA+B,CAAC;YACtC,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;QACR,CAAC;aAAM,CAAC;YACN,GAAG,GAAG,OAAO,CAAC,GAA2B,CAAC;YAC1C,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,cAAc;QACd,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAElB,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;QAC7B,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QAE9B,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,GAAG;YAC5B,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;YACrB,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;YAErB,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACvD,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACjD,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;gBAC5B,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAE,CAAC,GAAG,CAAC,CAAC;gBAEnE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAC"}